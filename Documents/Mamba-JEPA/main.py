# =================================================================================
# Production-Grade Mamba-JEPA with MLLM Knowledge Distillation (FIXED v3)
# =================================================================================
#
# This script implements the Mamba-JEPA architecture using a working Multimodal LLM
# as the "teacher" model for knowledge distillation.
#
# Fixed Issues:
# - Dynamic dimension handling for teacher features
# - Removed 4-bit quantization to avoid memory issues
# - Better error handling and fallback mechanisms
# - Proper dtype consistency throughout the pipeline
#
# =================================================================================

# -- 1. SETUP AND INSTALLATIONS --
import subprocess
import sys
import os

def install_package(package):
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package, "-q"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package}")
        return False

# Install packages
packages = [
    "transformers>=4.35.0",
    "datasets", 
    "accelerate", 
    "einops", 
    "timm", 
    "Pillow",
    "torchvision"
]

print("Installing required packages...")
for package in packages:
    install_package(package)

# Import libraries with error handling
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    from torch.utils.data import DataLoader, Dataset
    import torchvision
    import torchvision.transforms as transforms
    from transformers import (
        AutoModel, 
        AutoConfig, 
        Blip2Processor, 
        Blip2ForConditionalGeneration,
        ViTModel, 
        ViTImageProcessor
    )
    from tqdm.auto import tqdm
    from einops import rearrange
    import numpy as np
    import matplotlib.pyplot as plt
    from PIL import Image
    import warnings
    warnings.filterwarnings("ignore")
    print("✓ All imports successful")
except ImportError as e:
    print(f"✗ Import error: {e}")

# -- 2. CONFIGURATION --
class Config:
    # Model Hub Names
    TEACHER_MODEL_NAME = "Salesforce/blip2-opt-2.7b"
    STUDENT_MODEL_NAME = "state-spaces/mamba-130m"

    # Data Parameters
    IMAGE_SIZE = 224
    PATCH_SIZE = 16
    
    # Training Parameters
    BATCH_SIZE = 2
    NUM_EPOCHS = 3
    LEARNING_RATE = 1e-4
    
    # JEPA Parameters
    CONTEXT_RATIO = 0.6
    
    # Device settings
    DEVICE = "cuda" if torch.cuda.is_available() else "cpu"
    USE_4BIT = False  # Disabled to avoid bitsandbytes issues

config = Config()
print(f"Using device: {config.DEVICE}")
print(f"4-bit quantization: {config.USE_4BIT}")

# -- 3. CUSTOM DATASET CLASS --
class SimpleCIFAR10Dataset(Dataset):
    def __init__(self, num_samples=100):
        """Create a simple CIFAR-10 dataset using torchvision"""
        self.transform = transforms.Compose([
            transforms.Resize((config.IMAGE_SIZE, config.IMAGE_SIZE)),
            transforms.ToTensor(),
        ])
        
        # Try to load CIFAR-10, fallback to synthetic data if it fails
        try:
            self.dataset = torchvision.datasets.CIFAR10(
                root='./data', 
                train=True, 
                download=True, 
                transform=None  # We'll apply transform manually
            )
            self.use_real_data = True
            print(f"✓ CIFAR-10 loaded successfully")
        except Exception as e:
            print(f"✗ CIFAR-10 loading failed: {e}")
            print("Creating synthetic dataset...")
            self.use_real_data = False
            
        self.num_samples = min(num_samples, len(self.dataset) if self.use_real_data else num_samples)
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        if self.use_real_data:
            image, label = self.dataset[idx]
        else:
            # Create synthetic image if real data fails
            image = Image.fromarray(np.random.randint(0, 255, (32, 32, 3), dtype=np.uint8))
            label = np.random.randint(0, 10)
        
        return {
            'image': image,
            'label': label
        }

# -- 4. DATA LOADING --
def collate_fn(batch):
    """Custom collate function"""
    images = []
    labels = []
    
    for item in batch:
        img = item['image']
        if not isinstance(img, Image.Image):
            img = Image.fromarray(np.array(img))
        if img.mode != 'RGB':
            img = img.convert('RGB')
        images.append(img)
        labels.append(item['label'])
    
    return {
        'images': images,
        'labels': labels
    }

# Create dataset and dataloader
print("Creating dataset...")
try:
    dataset = SimpleCIFAR10Dataset(num_samples=200)  # Small dataset for demo
    dataloader = DataLoader(dataset, batch_size=config.BATCH_SIZE, collate_fn=collate_fn, shuffle=True)
    print(f"✓ Dataset created with {len(dataset)} samples")
except Exception as e:
    print(f"✗ Dataset creation failed: {e}")
    # Create a minimal fallback dataset
    class FallbackDataset(Dataset):
        def __len__(self):
            return 50
        def __getitem__(self, idx):
            # Create random RGB image
            img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
            return {'image': Image.fromarray(img_array), 'label': idx % 10}
    
    dataset = FallbackDataset()
    dataloader = DataLoader(dataset, batch_size=config.BATCH_SIZE, collate_fn=collate_fn)
    print("✓ Using fallback synthetic dataset")

# -- 5. MODEL LOADING WITH BETTER DIMENSION HANDLING --

# Teacher Model Loading
teacher_model = None
teacher_processor = None
teacher_dim = 768  # Will be dynamically determined

print("Loading teacher model...")
# Try BLIP-2 first (without 4-bit to avoid issues)
try:
    teacher_processor = Blip2Processor.from_pretrained(config.TEACHER_MODEL_NAME)
    teacher_model = Blip2ForConditionalGeneration.from_pretrained(
        config.TEACHER_MODEL_NAME,
        torch_dtype=torch.float32  # Use float32 to avoid issues
    ).to(config.DEVICE)
    
    for param in teacher_model.parameters():
        param.requires_grad = False
    teacher_model.eval()
    print("✓ BLIP-2 teacher model loaded")
    model_type = "blip2"
    
    # Test to get actual dimension
    with torch.no_grad():
        dummy_img = Image.fromarray(np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8))
        inputs = teacher_processor(images=[dummy_img], return_tensors="pt").to(config.DEVICE)
        vision_outputs = teacher_model.vision_model(**inputs)
        teacher_dim = vision_outputs.last_hidden_state.shape[-1]
        print(f"✓ Detected teacher dimension: {teacher_dim}")
    
except Exception as e:
    print(f"BLIP-2 loading failed: {e}")
    # Fallback to ViT
    try:
        teacher_processor = ViTImageProcessor.from_pretrained('google/vit-base-patch16-224')
        teacher_model = ViTModel.from_pretrained('google/vit-base-patch16-224').to(config.DEVICE)
        for param in teacher_model.parameters():
            param.requires_grad = False
        teacher_model.eval()
        teacher_dim = 768
        print("✓ ViT fallback teacher model loaded")
        model_type = "vit"
    except Exception as e2:
        print(f"ViT loading also failed: {e2}")
        print("Creating simple CNN teacher model...")
        # Ultimate fallback: simple CNN
        class SimpleCNN(nn.Module):
            def __init__(self):
                super().__init__()
                self.features = nn.Sequential(
                    nn.Conv2d(3, 64, 3, padding=1),
                    nn.ReLU(),
                    nn.AdaptiveAvgPool2d((7, 7)),
                    nn.Flatten(),
                    nn.Linear(64 * 7 * 7, 768)
                )
            def forward(self, x):
                return self.features(x)
        
        teacher_model = SimpleCNN().to(config.DEVICE)
        for param in teacher_model.parameters():
            param.requires_grad = False
        teacher_model.eval()
        teacher_processor = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        teacher_dim = 768
        print("✓ Simple CNN teacher model created")
        model_type = "cnn"

# Student Model Loading
print("Loading student model...")
try:
    student_config = AutoConfig.from_pretrained(config.STUDENT_MODEL_NAME)
    student_model = AutoModel.from_pretrained(config.STUDENT_MODEL_NAME).to(config.DEVICE).float()
    print("✓ Mamba student model loaded")
except Exception as e:
    print(f"Mamba loading failed: {e}")
    # Fallback: simple transformer
    class SimpleTransformer(nn.Module):
        def __init__(self, d_model=768):
            super().__init__()
            self.d_model = d_model
            self.transformer = nn.TransformerEncoder(
                nn.TransformerEncoderLayer(d_model=d_model, nhead=8, batch_first=True),
                num_layers=3
            )
            
        def forward(self, inputs_embeds):
            return type('Output', (), {'last_hidden_state': self.transformer(inputs_embeds)})()
    
    student_model = SimpleTransformer().to(config.DEVICE).float()
    student_config = type('Config', (), {'hidden_size': 768})()
    print("✓ Simple transformer student model created")

# -- 6. HELPER MODULES WITH DYNAMIC DIMENSIONS --
D_STUDENT = getattr(student_config, 'hidden_size', 768)
D_TEACHER = teacher_dim  # Use detected dimension

PATCH_DIM = 3 * config.PATCH_SIZE * config.PATCH_SIZE

print(f"Teacher dim: {D_TEACHER}, Student dim: {D_STUDENT}")

# Create modules with correct dimensions
patch_projector = nn.Linear(PATCH_DIM, D_STUDENT).to(config.DEVICE).float()
repr_projector = nn.Linear(D_TEACHER, D_STUDENT).to(config.DEVICE).float()
predictor = nn.Sequential(
    nn.Linear(D_STUDENT, D_STUDENT * 2),
    nn.GELU(),
    nn.Linear(D_STUDENT * 2, D_STUDENT)
).to(config.DEVICE).float()

# -- 7. TRAINING SETUP --
params_to_train = (
    list(student_model.parameters()) + 
    list(patch_projector.parameters()) + 
    list(repr_projector.parameters()) + 
    list(predictor.parameters())
)

optimizer = optim.AdamW(params_to_train, lr=config.LEARNING_RATE)
loss_fn = nn.MSELoss()

# -- 8. IMPROVED TRAINING FUNCTIONS --
def extract_teacher_features(images):
    """Extract features from teacher model with proper dimension handling"""
    with torch.no_grad():
        if model_type == "blip2":
            inputs = teacher_processor(images=images, return_tensors="pt").to(config.DEVICE)
            vision_outputs = teacher_model.vision_model(**inputs)
            # Use mean pooling over spatial dimensions
            features = vision_outputs.last_hidden_state.mean(dim=1)
            return features.float()
        elif model_type == "vit":
            inputs = teacher_processor(images=images, return_tensors="pt").to(config.DEVICE)
            outputs = teacher_model(**inputs)
            features = outputs.last_hidden_state[:, 0]  # CLS token
            return features.float()
        else:  # CNN fallback
            # Convert PIL to tensor
            tensors = []
            for img in images:
                tensor = teacher_processor(img)
                tensors.append(tensor)
            batch_tensor = torch.stack(tensors).to(config.DEVICE)
            features = teacher_model(batch_tensor)
            return features.float()

def create_patches(images):
    """Convert images to patches"""
    image_tensors = []
    for img in images:
        img = img.resize((config.IMAGE_SIZE, config.IMAGE_SIZE))
        img_tensor = torch.tensor(np.array(img), dtype=torch.float32) / 255.0
        img_tensor = img_tensor.permute(2, 0, 1)
        image_tensors.append(img_tensor)
    
    batch_tensor = torch.stack(image_tensors).to(config.DEVICE)
    patches = rearrange(
        batch_tensor, 'b c (h p1) (w p2) -> b (h w) (p1 p2 c)',
        p1=config.PATCH_SIZE, p2=config.PATCH_SIZE
    )
    return patches

# -- 9. TRAINING LOOP WITH BETTER ERROR HANDLING --
print("\nStarting training...")
print(f"Patch dimension: {PATCH_DIM}")
print(f"Number of patches per image: {(config.IMAGE_SIZE // config.PATCH_SIZE) ** 2}")

losses = []

student_model.train()
predictor.train()
patch_projector.train()
repr_projector.train()

for epoch in range(config.NUM_EPOCHS):
    pbar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{config.NUM_EPOCHS}")
    epoch_loss = 0.0
    batch_count = 0

    for batch in pbar:
        try:
            optimizer.zero_grad()
            
            images = batch['images']
            
            # Create patches
            patches = create_patches(images)
            num_patches = patches.shape[1]
            context_len = int(num_patches * config.CONTEXT_RATIO)
            context_patches = patches[:, :context_len, :].float()
            
            # Debug shapes
            if batch_count == 0:
                print(f"Debug - Patches shape: {patches.shape}")
                print(f"Debug - Context patches shape: {context_patches.shape}")
            
            # Student forward pass
            context_embeddings = patch_projector(context_patches)
            if batch_count == 0:
                print(f"Debug - Context embeddings shape: {context_embeddings.shape}")
            
            student_output = student_model(inputs_embeds=context_embeddings).last_hidden_state
            context_representation = student_output.mean(dim=1).float()
            
            if batch_count == 0:
                print(f"Debug - Context representation shape: {context_representation.shape}")
            
            # Predict target
            predicted_target = predictor(context_representation)
            
            # Get teacher features
            teacher_features = extract_teacher_features(images)
            if batch_count == 0:
                print(f"Debug - Teacher features shape: {teacher_features.shape}")
            
            true_target = repr_projector(teacher_features)
            if batch_count == 0:
                print(f"Debug - True target shape: {true_target.shape}")
                print(f"Debug - Predicted target shape: {predicted_target.shape}")
            
            # Loss
            loss = loss_fn(predicted_target, true_target)
            
            # Backward
            loss.backward()
            torch.nn.utils.clip_grad_norm_(params_to_train, max_norm=1.0)
            optimizer.step()
            
            epoch_loss += loss.item()
            batch_count += 1
            pbar.set_postfix({'loss': f'{loss.item():.6f}'})
            
        except Exception as e:
            print(f"Batch error: {e}")
            if batch_count == 0:  # Print debug info for first batch error
                import traceback
                traceback.print_exc()
            continue
    
    if batch_count > 0:
        avg_loss = epoch_loss / batch_count
        losses.append(avg_loss)
        print(f"Epoch {epoch+1}: Loss = {avg_loss:.6f}")
    else:
        print(f"Epoch {epoch+1}: No successful batches")

# -- 10. RESULTS --
if losses:
    plt.figure(figsize=(10, 5))
    plt.plot(losses, 'b-', linewidth=2, marker='o')
    plt.title("Mamba-JEPA Training Loss", fontsize=14)
    plt.xlabel("Epoch")
    plt.ylabel("MSE Loss")
    plt.grid(True, alpha=0.3)
    plt.show()
    
    print(f"\n🎉 Training completed!")
    print(f"Final loss: {losses[-1]:.6f}")
    if len(losses) > 1:
        improvement = ((losses[0] - losses[-1]) / losses[0] * 100)
        print(f"Improvement: {improvement:.1f}%")
else:
    print("❌ Training failed")

print("\n--- Mamba-JEPA PoC Complete ---")